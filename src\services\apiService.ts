'use server'

import { getServerAuthSession } from '@/config/auth'
import { env } from '@/config/environment'
import { getLocale } from 'next-intl/server'

export interface ApiServiceProps extends RequestInit {
  path: string
  searchParams?: Record<string, any>
  locale?: string
  revalidate?: number | false
  enabled?: boolean
}

export async function apiService({ path, searchParams, revalidate, enabled = true, ...props }: ApiServiceProps) {
  // Return early if disabled
  if (!enabled) {
    return { data: null }
  }

  try {
    const user = await getServerAuthSession()

    const locale = await getLocale()

    // Validate BASE_API environment variable
    if (!env.BASE_API) {
      console.error('BASE_API environment variable is not set')
    }

    const headers = {
      ...props.headers,
      'Accept-Language': locale || 'en',
      Accept: 'application/json',
      ...(user?.user.token && { Authorization: `Bearer ${user?.user.token}` }),
    }

    const BASE_URL = env.BASE_API
    const urlSearchParams = new URLSearchParams(searchParams)
    const url = `${BASE_URL}${path}${!!urlSearchParams.size ? `?${new URLSearchParams(searchParams)}` : ''}`

    const res = await fetch(url, {
      ...props,
      method: props.method || 'GET',
      headers,
      next: {
        tags: [path, urlSearchParams.toString() || ''],
        ...(revalidate !== undefined && { revalidate }),
        ...props.next,
      },
    })

    if (res.ok) {
      const jsonRes = await res.json()
      console.log('req success log:', path, res.status, jsonRes)

      return jsonRes
    } else {
      console.log('❌ Request failed:', path, res.status, res.statusText)

      // Try to get error details from response
      let errorDetails = ''
      try {
        const errorRes = await res.text()
        errorDetails = errorRes
      } catch (e) {
        errorDetails = 'Unable to read error response'
      }

      console.log('❌ Error details:', errorDetails)

      if (res.status === 401) {
        // Don't call signOut on server side as it accesses window object
        // Instead, return an error that can be handled on the client side
        return { 
          status: 401, 
          error: 'Unauthorized', 
          message: 'Please log in again' 
        }
      } else {
        // new Error("Not Found");
      }
    }
  } catch (e) {
    console.log('🚀 ~ apiService ~ error when getting data:', path, e)
  }
}
