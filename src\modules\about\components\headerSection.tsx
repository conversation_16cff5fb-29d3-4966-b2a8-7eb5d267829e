import { ComponentProps } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

// types
import { IDoctorInfo } from '@/types/doctorInfo'

// shared
import { WorkingSchedules } from '@/components/shared/workingSchedules'
import { DoctorContactLinks } from '@/components/shared/doctorContactLinks'
import SectionTitle from '@/components/shared/sectionTitle'
import DownloadDoctorCv from '@/components/shared/downloadDoctorCv'
import IconWithBg from '@/components/shared/iconWithBg'

// icons
import AcademicBackgroundIcon from '/public/icons/degree.svg'
import WorkExperienceIcon from '/public/icons/experience.svg'
import { cn } from '@/utils/cn'
import { Button } from '@/components/ui/button'

export default function HeaderSection({
  doctorInfo,
  className,
}: {
  doctorInfo: IDoctorInfo
  className?: ComponentProps<'section'>['className']
}) {
  const t = useTranslations()
  return (
    <section
      className={cn('container grid md:grid-cols-2 grid-cols-1 lg:gap-12 md:gap-8 gap-5 items-start ', className)}
    >
      <article className="flex flex-col gap-4 rounded-3xl p-4 justify-between items-center bg-primary-bg lg:py-8 md:py-6 py-4">
        {doctorInfo?.about?.image ? (
          <Image
            src={doctorInfo?.about?.image}
            alt={t('name')}
            className=" max-h-[150px] rounded-full object-cover object-top size-[150px]"
            width={150}
            height={150}
            priority
          />
        ) : (
          <div className="w-full h-[150px] max-w-[150px]  bg-gray-01 rounded-3xl size-[150px]" />
        )}

        <div className="flex flex-col lg:gap-6 md:gap-4 gap-3">
          <DoctorContactLinks
            className="lg:col-span-2 col-span-1 lg:gap-6 md:gap-4 gap-3"
            {...doctorInfo}
            title={t('about.contact_info')}
            titleClassName="text-primary-02"
            listClassName="!space-y-1"
          />

          {doctorInfo?.schedule && doctorInfo?.schedule.length > 0 && (
            <WorkingSchedules
              doctorSchedule={doctorInfo.schedule}
              title={t('about.working_schedules')}
              className="lg:gap-6 md:gap-4 gap-3"
              titleClassName="text-primary-02"
            />
          )}
        </div>

        <Button className="rounded-full px-8 py-3 text-lg font-semibold ">{t('button.book_now')}</Button>
      </article>

      <article className="flex flex-col lg:gap-8 md:gap-6 gap-4">
        <SectionTitle title={t('name')} />
        <p className="text-primary-03 lg:text-2xl sm:text-xl text-lg font-normal">{doctorInfo?.about?.content}</p>

        {/* Academic Background and Training */}
        <div className="flex justify-start items-start gap-4">
          <IconWithBg
            className="bg-action-02-bg"
            icon={<Image src={AcademicBackgroundIcon} alt="Academic Background" width={24} height={24} />}
          />
          <div className="flex flex-col gap-4">
            <h3 className="flex justify-start gap-2 items-center text-primary-03 lg:text-[28px] md:text-[24px] sm:text-[20px] text-[18px] font-normal">
              {t('about.academic_background_and_training')}
            </h3>

            <ul className="list-inside list-disc space-y-2">
              {doctorInfo?.profile?.academic_background.map((item) => (
                <li key={item.title}>
                  <div className="inline-flex flex-col">
                    <p className="text-primary-03 text-lg">{item.title}</p>
                    <p className="text-gray-01 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal ">{`${item.from} - ${item.to}`}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Work Experience */}
        <div className="flex justify-start items-start gap-4">
          <IconWithBg
            className="bg-action-04-bg"
            icon={<Image src={WorkExperienceIcon} alt="Work Experience" width={24} height={24} />}
          />
          <div className="flex flex-col gap-4">
            <h3 className="flex justify-start gap-2 items-center text-primary-03 lg:text-[28px] md:text-[24px] sm:text-[20px] text-[18px] font-normal">
              {t('about.work_experience')}
            </h3>

            <ul className="list-inside list-disc space-y-2">
              {doctorInfo?.profile?.work_experiences.map((item) => (
                <li key={item.title}>
                  <div className="inline-flex flex-col">
                    <p className="text-primary-03 text-lg">{item.title}</p>
                    <p className="text-gray-01 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal ">{`${item.from} - ${item.to}`}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <DownloadDoctorCv doctorInfo={doctorInfo?.about} />
      </article>
    </section>
  )
}
