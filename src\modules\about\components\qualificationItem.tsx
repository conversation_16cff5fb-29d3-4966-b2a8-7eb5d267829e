import IconWithBg from '@/components/shared/iconWithBg'
import { IQualification } from '@/types'
import { Award } from 'lucide-react'

export default function QualificationItem({ qualification }: { qualification: IQualification }) {
  return (
    <article className="flex justify-start items-start gap-2 p-4 lg:py-6 rounded-xl basic_card_shadow">
      <IconWithBg className="bg-action-02-bg" icon={<Award className="fill-action-02 stroke-action-02" size={32} />} />

      <div className="inline-flex flex-col">
        <p className="text-primary-03 lg:text-2xl md:text-xl text-lg">{qualification.title}</p>
        <p className="text-gray-01 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal ">
          {qualification.description}
        </p>
        <p className="text-gray-03 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal ">
          {qualification.date}
        </p>
      </div>
    </article>
  )
}
