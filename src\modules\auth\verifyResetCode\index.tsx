'use client'

import { verifyResetCodeSchema } from '../schema'
import Counter from './components/Counter'
import useVerifyResetCode from './useVerifyResetCode'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInputOTP } from '@/components/form/FormInputOTP'
import { useCounter } from './components/Counter'

const VerifyResetCode = () => {
  const { email, defaultValues, onSubmit, t, handleResendCode, formRef, isPending, isAllowed } = useVerifyResetCode()
  const { isCounterActive } = useCounter()

  return (
    <>
      <HeaderPage title="verification_code" />
      <div className="text-center text-[22px] text-primary-03 max-w-[380px]">
        {t('auth.enter_code_email')} {email || '***'}
      </div>
      <FormWrapper ref={formRef} defaultValues={defaultValues} onSubmit={onSubmit} schema={verifyResetCodeSchema}>
        <div className="flex flex-col gap-4 items-center">
          {isAllowed && <Counter />}
          <FormInputOTP name="code" label={t('label.code')} />
        </div>
        <Button type="submit" className="block mt-9 mb-3 mx-auto" disabled={isPending}>
          {isPending ? (
            <div className="flex items-center justify-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {t('auth.sending')}
            </div>
          ) : (
            t('auth.verify')
          )}
        </Button>
        <Button
          variant="secondary"
          type="button"
          onClick={handleResendCode}
          className="underline p-0 w-full"
          disabled={!isAllowed || isCounterActive || isPending}
        >
          {t('auth.resend_code')}
        </Button>
      </FormWrapper>
    </>
  )
}

export default VerifyResetCode
