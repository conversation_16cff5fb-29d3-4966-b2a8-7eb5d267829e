import { IQualification } from "./qualifications";

// Contact Information Interface
export interface IContactInfo {
  email: string;
  phone: string;
  whatsapp: string;
}

// Social Media Links Interface
export interface ISocialMedia {
  facebook: string;
  twitter: string;
  instagram: string;
  youtube: string;
}

// About Section Interface
export interface IAboutSection {
  content: string;
  image: string;
  profile_pdf: string;
}

// Academic Background Item Interface
export interface IAcademicBackground {
  title: string;
  from: string;
  to: string;
}

// Work Experience Item Interface
export interface IWorkExperience {
  title: string;
  from: string;
  to: string;
}

// Profile Section Interface
export interface IProfileSection {
  bio: string;
  image: string;
  academic_background: IAcademicBackground[];
  work_experiences: IWorkExperience[];
}

// Clinic Information Interface
export interface IClinic {
  id: string;
  name: string;
  waiting_time: string;
  address: string;
  location: string;
}

// Main Doctor Information Interface
export interface IDoctorInfo {
  email: string;
  phone: string;
  whatsapp: string;
  social: ISocialMedia;
  about: IAboutSection;
  profile: IProfileSection;
  clinics: IClinic[];
  schedule: string[];
  qualifications: IQualification[];
}
