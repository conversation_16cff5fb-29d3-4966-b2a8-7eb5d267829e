import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'
import { ILogin } from '@/types'
import { signIn } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'

import { toast } from 'sonner'

const useLogin = () => {
  const t = useTranslations()
  const { isPending } = useApi({
    path: '/auth/login',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (state) => {
      toast.success(t('auth.login_success'))
      redirect(Routes.HOME)
    },
  })

  const handleSubmit = async (payload: ILogin) => {
    await signIn('credentials', {
      redirect: false,
      email: payload.email,
      password: payload.password,
    })
  }

  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useLogin
