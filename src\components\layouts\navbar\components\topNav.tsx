import { ComponentProps, ReactNode } from 'react'
import { cn } from '@/utils/cn'

// components
import { SocialLinks } from '@/components/shared/socialLinks'

// types
import { IDoctorInfo } from '@/types/doctorInfo'

// icons
import { PhoneIcon } from 'lucide-react'
import { EmailIcon } from '@/components/icons/social/EmailIcon'
import { useTranslations } from 'next-intl'

function TopNav({
  doctorInfo,
  className,
}: {
  doctorInfo: IDoctorInfo
  className?: ComponentProps<'div'>['className']
}) {
  const t = useTranslations()

  return (
    <div
      className={cn(
        'container flex items-center justify-between py-4 border-b border_gradient max-md:flex-col',
        className
      )}
    >
      <SocialLinks {...doctorInfo?.social} whatsapp={doctorInfo?.whatsapp} />

      <div className="flex justify-content-between gap-2 items-center">
        <IconLinksWithIcon
          icon={<EmailIcon className="size-5" />}
          link={`mailto:${doctorInfo?.email}`}
          text={doctorInfo?.email}
          title={t('nav.email')}
        />
        <IconLinksWithIcon
          icon={<PhoneIcon className="size-4 fill-gray-01 stroke-gray-01" />}
          link={`tel:${doctorInfo?.phone}`}
          text={doctorInfo?.phone}
          dir={'ltr'}
          title={t('nav.phone')}
        />
      </div>
    </div>
  )
}

const IconLinksWithIcon = ({
  icon,
  link,
  text,
  ...props
}: {
  icon: ReactNode
  link: string
  text: string
} & ComponentProps<'a'>) => {
  return (
    <div className="flex items-center gap-1">
      <a
        className={cn('text-primary-03 font-normal lg:text-lg sm:text-base text-sm', props.className)}
        href={link}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {text}
      </a>
      {icon}
    </div>
  )
}

export { TopNav, IconLinksWithIcon }
