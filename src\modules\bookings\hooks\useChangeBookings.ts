import { useSearchParams } from 'next/navigation'

import { useRef } from 'react'
import { useQuery } from '@/store/queryContext/useQueryContext'
import { IBookingData } from '@/types/bookings'
import { useEffect } from 'react'

function useChangeBookings(bookingData: IBookingData, resetBookingData: () => void) {
  const searchParams = useSearchParams()
  const hasInitialized = useRef(false)

  const { forwardAddQuery, forwardDeleteQuery, forwardQuery } = useQuery()

  const type = searchParams.get('type') || 'online'
  const clinic_id = searchParams.get('clinic') || bookingData?.clinics[0]?.id
  const date = searchParams.get('date') || new Date().toISOString().split('T')[0]

  const handleTabChange = (value: string) => {
    resetBookingData();
    
    if (value === 'offline') {
      // When switching to offline, add clinic if not present
      const updates: Record<string, string> = { type: value }
      if (!searchParams.get('clinic')) {
        updates.clinic = bookingData?.clinics[0]?.id || ''
      }
      forwardAddQuery(updates)
    } else {
      forwardDeleteQuery('clinic')
    }
  }

  useEffect(() => {
    if (!forwardQuery?.['clinic'] && forwardQuery?.['type'] != 'online') forwardAddQuery({ type: 'online' })
  }, [forwardQuery])

  useEffect(() => {
    if (hasInitialized.current) return

    const updates: Record<string, string> = {}
    const currentType = searchParams.get('type') || 'online'

    if (!searchParams.get('type')) {
      updates.type = 'online'
    }
    if (!searchParams.get('date')) {
      updates.date = new Date().toISOString().split('T')[0]
    }

    if (currentType === 'offline' && !searchParams.get('clinic')) {
      updates.clinic = bookingData?.clinics[0]?.id || ''
    } else if (currentType === 'online' && searchParams.get('clinic')) {
      forwardDeleteQuery('clinic')
    }

    if (Object.keys(updates).length > 0) {
      forwardAddQuery(updates)
    }

    hasInitialized.current = true
  }, [searchParams, bookingData?.clinics, forwardAddQuery, forwardDeleteQuery])

  return {
    handleTabChange,
    type,
    date,
    clinic_id,
  }
}

export { useChangeBookings }
