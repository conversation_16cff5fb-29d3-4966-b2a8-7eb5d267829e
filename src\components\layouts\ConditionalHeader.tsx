'use client'

import { usePathname } from 'next/navigation'

// Components
import { Navbar } from '@/components/layouts/navbar'
import HomeHeader from '@/modules/home/<USER>/homeHeader'
import { IDoctorInfo } from '@/types'

export default function ConditionalHeader({doctorInfo}: {doctorInfo: IDoctorInfo}) {
  const pathname = usePathname()

  // Check if current page is home page (considering language prefix)
  const isHomePage = pathname === '/' || pathname.match(/^\/[a-z]{2}$/) || pathname.match(/^\/[a-z]{2}\/$/)

  return <>{isHomePage ? <HomeHeader doctorInfo={doctorInfo} /> : <Navbar  className={'base_gradient'} doctorInfo={doctorInfo} />}</>
}
