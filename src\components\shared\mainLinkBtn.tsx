import { cn } from '@/lib/utils'
import Link from 'next/link'
import { ComponentProps } from 'react'

export default function MainLinkBtn({
  link,
  className,
  text,
}: {
  link: string
  className?: ComponentProps<'link'>['className']
  text: string
}) {
  return (
    <Link
      href={link}
      className={cn(
        'main_btn_gradient text-white max-h-[48px] lg:px-8 md:px-6 px-4 lg:py-3 py-2 rounded-3xl lg:w-[197px] w-[180px] text-center font-bold',
        className
      )}
    >
      {text}
    </Link>
  )
}
