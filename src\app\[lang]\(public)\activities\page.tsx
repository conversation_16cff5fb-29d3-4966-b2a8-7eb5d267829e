import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { apiService } from '@/services'

import SectionTitle from '@/components/shared/sectionTitle'
import { IActivity } from '@/types/activities'
import { ActivityItem } from '@/components/shared/activitiyItem'

export const metadata: Metadata = {
  title: 'Activities',
}

export default async function ActivitiesPage() {
  const t = await getTranslations()

  const data = await apiService({
    path: '/activities',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })
  return (
    <>
      {data?.data && data?.data?.items.length > 0 && (
        <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
          <SectionTitle title={t('nav.activities')} className="text-center" />
          {data?.data?.items?.map((activity: IActivity, index: number) => (
            <ActivityItem key={activity.title} {...activity} index={index} className="w-full" />
          ))}
        </section>
      )}
    </>
  )
}
