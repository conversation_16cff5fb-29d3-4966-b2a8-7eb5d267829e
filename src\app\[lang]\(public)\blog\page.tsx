import BlogList from '@/modules/blog'
import { apiService } from '@/services'
import { IArticle } from '@/types'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Blog',
  description: 'Explore our latest articles, insights, and updates on technology, design, and development.',
}

const Blog = async ({ searchParams }: IPageProps) => {
  const _searchParams = await searchParams
  const blog: { data: TData<IArticle> } = await apiService({
    path: `/articles`,
    searchParams: _searchParams,
    next: { tags: ['articles'] },
  })
  return <BlogList data={blog.data} />
}

export default Blog
