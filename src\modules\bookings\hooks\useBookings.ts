import { useSearchParams } from 'next/navigation'
import { useState } from 'react'
import { useLocale, useTranslations } from 'next-intl'
import { ICreatBooking } from '@/types'

import { toast } from 'sonner'
import useApi from '@/hooks/useApi'

export default function useBookings() {
  const t = useTranslations()
  const date = useSearchParams().get('date') || new Date().toISOString().split('T')[0]
  const type = useSearchParams().get('type') || 'online'
  const clinic_id = useSearchParams().get('clinic')

  const locale = useLocale()
  const day = new Date(date).toLocaleDateString(locale, { weekday: 'long' })

  const [selectedBooking, setSelectedBooking] = useState<ICreatBooking | null>(null)

  const handleBookingSelect = (from: string, to: string) => {
    setSelectedBooking({
      from,
      to,
      type : type as 'online' | 'offline',
      date,
      ...(type === 'offline' && clinic_id && { clinic_id }),
    })
  }

  // Function to check if a specific booking is selected
  const isBookingSelected = (from: string, to: string) => {
    if (selectedBooking?.from === from && selectedBooking?.to === to) {
      return true
    }
    return false
  }

  // Function to clear selection
  const resetSelection = () => {
    setSelectedBooking(null)
  }


    const { action, isPending } = useApi({
    path: '/bookings',
    method: 'POST',
      handleSuccess: false,
    onSuccess: (state) => {
      toast.success(t('bookings.booking_success'))
      },
    
 
  })

  const redirectToPayment = () => {
    if (!selectedBooking) {
      return toast.error(t('bookings.must_select_booking'))
    }
    console.log(selectedBooking)
  const formdata = new FormData()
    formdata.append('type', selectedBooking.type)
    formdata.append('date', selectedBooking.date)
    formdata.append('from', selectedBooking.from)
    formdata.append('to', selectedBooking.to)
    if(type === 'offline') {
      formdata.append('clinic_id', selectedBooking.clinic_id as string)
    }
console.log(formdata)
    action(formdata)
  }

  return {
    day,
    date,
    selectedBooking,
    handleBookingSelect,
    isBookingSelected,
    resetSelection,
    redirectToPayment,
    redirectToPaymentLoading: isPending
  }
}
