'use client'

// types
import { IBookingData, IAvailableBookings } from '@/types'

// hooks
import { useTranslations } from 'next-intl'

// components
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// booking components
import TabContentSuspense from '@/modules/bookings/components/TabContentSuspense'
import BookingCarousel from './components/bookingCarousel'

// filter components
import FilterSelect from '@/components/filters/FilterSelect'
import DatePickerFilter from '@/components/filters/DatePickerFilter'

// custom hooks
import { useChangeBookings } from '@/modules/bookings/hooks/useChangeBookings'
import useBookings from '@/modules/bookings/hooks/useBookings'
import { useGetBookingData } from './hooks/useGetBookingData'
import { cn } from '@/utils/cn'
import { Button } from '@/components/ui/button'

interface IBookingsProps {
  bookingData: IBookingData
  initialAvailableBookings: IAvailableBookings[]
}

export default function Bookings({ bookingData, initialAvailableBookings }: IBookingsProps) {
  const t = useTranslations()

  const today = new Date()
  const next30 = new Date()
  next30.setDate(today.getDate() + Number(bookingData?.availability_days ?? 30))

  // hooks
  const { day, handleBookingSelect, isBookingSelected, redirectToPayment, resetSelection, redirectToPaymentLoading } =
    useBookings()

  const { handleTabChange, type, date, clinic_id } = useChangeBookings(bookingData, resetSelection)

  const { data: availableBookings, loading: availableBookingsLoading } = useGetBookingData({
    date,
    clinic: clinic_id,
    type,
    initialData: initialAvailableBookings,
  })

  const tabs = [
    {
      label: t('bookings.online'),
      value: 'online',
      component: (
        <BookingCarousel
          date={date}
          day={day}
          availableBookings={availableBookings || []}
          onBookingSelect={(from, to) => handleBookingSelect(from, to)}
          isBookingSelected={isBookingSelected}
        />
      ),
    },
    {
      label: t('bookings.offline'),
      value: 'offline',
      component: (
        <BookingCarousel
          date={date}
          day={day}
          availableBookings={availableBookings || []}
          onBookingSelect={(from, to) => handleBookingSelect(from, to)}
          isBookingSelected={isBookingSelected}
        />
      ),
    },
  ]

  return (
    <>
      <Tabs
        defaultValue={type as string}
        onValueChange={handleTabChange}
        className="w-full items-center container gap-2"
      >
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger
              key={`title-${tab.value}`}
              className="data-[state=active]:bg-transparent data-[state=active]:text-primary-02 data-[state=active]:shadow-none lg:text-2xl md:text-xl text-lg text-gray-01"
              value={tab.value}
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        <DatePickerFilter
          name="date"
          placeholder={t('bookings.select_date')}
          mode="single"
          queryName="date"
          defaultValue={new Date(date) as unknown as string}
          disabledDates={{ before: today, after: next30 }}
        />

        {bookingData?.time_slot && <p>{t('bookings.waiting_time', { waiting_time: bookingData?.time_slot })}</p>}

        <div className={cn('flex justify-center items-center gap-0.5', type === 'online' && 'min-h-[36px]')}>
          {type === 'offline' && (
            <>
              <FilterSelect
                placeholder={t('bookings.select_clinic')}
                name="clinic"
                data={bookingData?.clinics}
                valueKey="id"
                labelKey="name"
                defaultValue={clinic_id}
                hasResetBtn={false}
              />
              <p className="text-primary-02 lg:text-2xl sm:tex-xl font-bold text-base">{t('bookings.select_clinic')}</p>
            </>
          )}
        </div>
        <TabContentSuspense availableBookings={availableBookings} isLoading={availableBookingsLoading}>
          {tabs.map((tab) => (
            <TabsContent key={`content-${tab.value}`} value={tab.value} style={{ maxWidth: '-webkit-fill-available' }}>
              {tab.component}
            </TabsContent>
          ))}
        </TabContentSuspense>
      </Tabs>

      <div className="flex justify- flex-col gap-1">
        <Button className="disabled" isLoading={redirectToPaymentLoading} onClick={() => redirectToPayment()}>
          {t('button.redirect_to_payment')}
        </Button>
      </div>
    </>
  )
}
