// ** Third Party Imports
import axios from 'axios'
import { AuthOptions, getServerSession } from 'next-auth'
import Credentials<PERSON>rovider from 'next-auth/providers/credentials'

import { env } from '@/config/environment'
import { actionService } from '@/services'

let expires = 60 * 60 // ** 1hour default

export const authOptions: AuthOptions = {
  // JWT Secret from env variable
  secret: env.NEXT_PUBLIC_SECRET,

  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      type: 'credentials',
      credentials: {},
      async authorize(credentials) {
        try {
          const res: any = await actionService(
            {
              path: '/auth/login',
              method: 'POST',
            },
            null,
            credentials
          )

          const user = res?.data?.data

          if (res.status === 200 && user) {
            return user
          }

          return null
        } catch (err) {
          const { response } = err as any
          throw response?.data?.Error
        }
      },
    }),
  ],

  session: {
    strategy: 'jwt' as const,
    maxAge: expires, // ** 1 hour
  },

  pages: {
    signIn: '/auth/login',
    signOut: '/',
    error: '/auth/login',
  },

  callbacks: {
    async jwt({ token, user, session, trigger }: any) {
      if (user) {
        /*
         * For adding custom parameters to user in session, we first need to add those parameters
         * in token which then will be available in the `session()` callback
         */
        token.user = user
      }

      if (trigger === 'update') {
        token.user = session
      }

      // The Token takes it's value from user data when logged in. If the session has been modified, We have to update the token to match the session value.
      return token
    },
    async session({ session, token }) {
      if (session.user) {
        // ** Add custom params to user in session which are added in `jwt()` callback via `token` parameter
        session.user = token.user
      }

      return session
    },
  },
}

export const getServerAuthSession = () => getServerSession(authOptions)
