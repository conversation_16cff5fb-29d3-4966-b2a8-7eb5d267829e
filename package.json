{"name": "base_nextjs", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint src --fix && yarn format", "lint:strict": "eslint --max-warnings=0", "typecheck": "tsc --noEmit --incremental false", "prepare": "husky"}, "lint-staged": {"*.{jsx,js,ts,tsx}": []}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.64.2", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "4.3.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.0.1", "input-otp": "^1.4.2", "lucide-react": "^0.542.0", "negotiator": "^0.6.3", "next": "^15.1.6", "next-auth": "^4.24.7", "next-intl": "^3.12.2", "next-pwa": "^5.6.0", "next-themes": "^0.3.0", "react": "^19.0.0", "react-day-picker": "^9.5.0", "react-dom": "^19.0.0", "react-hook-form": "^7.51.3", "sonner": "^1.4.41", "tailwind-merge": "^2.3.0", "vaul": "^1.1.2", "yup": "^1.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@ianvs/prettier-plugin-sort-imports": "4.4.1", "@next/eslint-plugin-next": "^15.1.6", "@tanstack/react-query-devtools": "^5.34.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.5", "@types/negotiator": "^0.6.3", "@types/node": "^20.12.7", "@types/react": "19.0.7", "@types/react-dom": "19.0.3", "@typescript-eslint/eslint-plugin": "8.21.0", "@typescript-eslint/parser": "8.21.0", "autoprefixer": "^10.4.19", "eslint": "9.18.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "10.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.38", "prettier": "3.4.2", "prettier-plugin-sort-json": "4.1.1", "sass": "^1.76.0", "tailwindcss": "^3.4.3", "typescript": "5.7.3"}}