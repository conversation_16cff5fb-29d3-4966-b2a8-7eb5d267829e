import { ComponentProps } from 'react'

import { cn } from '@/lib/utils'

// icons
import CalenderIcon from '@/components/icons/CalenderIcon'
import { TimeIcon } from '@/components/icons/timeIcon'

export interface IBookItemProps {
  day: string
  from: string
  to: string
  date: string
  isSelected: boolean
  className?: ComponentProps<'div'>['className']
  onClick: (from: string, to: string) => void
}

export default function AvailableBookItemCard({ day, from, to, date, isSelected, className, onClick }: IBookItemProps) {
  return (
    <div
      className={cn(
        'flex flex-col justify-start items-start gap-2 bg-primary-bg p-4 rounded-3xl  cursor-pointer',
        isSelected && 'border-2 border-primary-02',
        className
      )}
      onClick={() => onClick(from, to)}
    >
      <h5 className="text-primary-02 font-bold lg:text-3xl md:text-2xl sm:text-xl text-lg text-center w-full">{day}</h5>

      <p
        className={cn(
          'text-primary-03 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal flex justify-start items-center gap-1',
          className
        )}
      >
        <TimeIcon className={cn('size-4 fill-gray-01', isSelected && ' fill-primary-02')} />
        {`${from} - ${to}`}
      </p>

      <p
        className={cn(
          'text-primary-03 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal flex justify-start items-center gap-1',
          className
        )}
      >
        <CalenderIcon className={cn('size-4 fill-gray-01', isSelected && ' fill-primary-02')} />
        {date}
      </p>
    </div>
  )
}
