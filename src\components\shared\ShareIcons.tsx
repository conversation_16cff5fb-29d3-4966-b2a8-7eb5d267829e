'use client'

import Image from 'next/image'
import Link from 'next/link'
import InstagramIcon from '/public/icons/insta.svg'
import FacebookIcon from '/public/icons/facebook.svg'
import XIcon from '/public/icons/x.svg'

const ShareIcons = ({ id }: { id: number }) => {
  console.log(window.location.origin)
  const fullUrl = `${typeof window !== 'undefined' ? window.location.origin : ''}/blog/${id}`
  const shareUrl = encodeURIComponent(fullUrl)
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`
  const xUrl = `https://twitter.com/intent/tweet?url=${shareUrl}`
  const instagramUrl = 'https://www.instagram.com'
  return (
    <div className="flex gap-2 items-center h-fit">
      <Link href={xUrl} target="_blank" rel="noopener noreferrer">
        <div className="relative w-8 h-8">
          <Image src={XIcon} alt="X icon" fill className="object-contain" />
        </div>
      </Link>
      <Link href={instagramUrl} target="_blank" rel="noopener noreferrer">
        <div className="relative w-8 h-8">
          <Image src={InstagramIcon} alt="Instagram icon" fill className="object-contain" />
        </div>
      </Link>
      <Link href={facebookUrl} target="_blank" rel="noopener noreferrer">
        <div className="relative w-8 h-8">
          <Image src={FacebookIcon} alt="Facebook icon" fill className="object-contain" />
        </div>
      </Link>
    </div>
  )
}

export default ShareIcons
