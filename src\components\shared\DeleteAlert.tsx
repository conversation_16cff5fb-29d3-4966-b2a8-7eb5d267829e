import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

import { useTranslations } from 'next-intl'

import { useImperativeHandle, useState, type Ref, type ReactNode } from 'react'

interface DeleteAlertProps {
  title: string | ReactNode
  body: string | ReactNode
  onDelete: () => void
  onCancel?: () => void
}

interface DeleteAlertRef {
  open: boolean
  handleOpen: (value: boolean) => void
}

const DeleteAlert = ({ title, body, onDelete, onCancel, ...props }: DeleteAlertProps, ref: Ref<DeleteAlertRef>) => {
  const t = useTranslations()
  const [open, setOpen] = useState(false)

  const handleOpen = (value: boolean) => {
    setOpen(value)
  }

  useImperativeHandle(ref, () => ({
    open,
    handleOpen,
  }))

  return (
    <AlertDialog {...props}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{body}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={() => {
              onCancel && onCancel()
              handleOpen(false)
            }}
          >
            {t('button.cancel')}
          </AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>{t('button.confirm')}</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default DeleteAlert
