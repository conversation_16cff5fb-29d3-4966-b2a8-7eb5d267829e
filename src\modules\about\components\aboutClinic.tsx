import SectionTitle from '@/components/shared/sectionTitle'
import { cn } from '@/utils/cn'
import { useTranslations } from 'next-intl'
import Image, { StaticImageData } from 'next/image'
import { ComponentProps } from 'react'

interface AboutClinicProps {
  title: string
  image: string | StaticImageData
  className?: ComponentProps<'section'>['className']
  items: {
    title: string
    icon: React.ReactNode
  }[]
}
export default function AboutClinic({ title, image, items, className }: AboutClinicProps) {
  const t = useTranslations()
  return (
    <section className={cn('container flex justify-between gap-4 lg:gap-8 items-center max-md:flex-wrap', className)}>
      {image ? (
        <Image
          src={image}
          alt={t('name')}
          className="rounded-3xl  h-auto max-h-[651px] lg:min-h-[651px] object-cover lg:!basis-1/2 !basis-full"
          width={548}
          height={479}
          priority
        />
      ) : (
        <div className="w-full h-[651px]   bg-gray-01 rounded-3xl lg:!basis-1/2 !basis-full" />
      )}

      <article className="flex justify-between items-start flex-col !basis-full lg:!basis-1/2 gap-4">
        <SectionTitle title={title} />

        <div className="grid sm:grid-cols-2 grid-cols-1 gap-6">
          {items.map((item) => (
            <div key={item.title} className="flex flex-col justify-center items-start gap-2">
              {item.icon}
              <p className="text-primary-03 lg:text-2xl sm:xl text-lg font-normal">{item.title}</p>
            </div>
          ))}
        </div>
      </article>
    </section>
  )
}
