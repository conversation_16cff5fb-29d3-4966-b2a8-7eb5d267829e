import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { apiService } from '@/services'

import SectionTitle from '@/components/shared/sectionTitle'
import Bookings from '@/modules/bookings'

import QueryProvider from '@/store/queryContext/queryContext'

export const metadata: Metadata = {
  title: 'Bookings',
}

export default async function BookingsPage({ searchParams }: IPageProps) {
  const t = await getTranslations()
  const { date, clinic, type } = await searchParams

  const [bookingData, availableBookings] = await Promise.all([
    apiService({
      path: '/booking-data',
      revalidate: 1800,
    }),
    apiService({
      path: '/get-available-bookings',
      searchParams: {
        date: date || new Date().toISOString().split('T')[0],
        ...(clinic && type === 'offline' && { clinic_id: clinic }),
        type: type || 'online',
      },
      revalidate: 800,
    }),
  ])

  return (
    <>
      {bookingData?.data && (
        <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
          <div>
            <SectionTitle title={t('nav.bookings')} className="text-center" />
            {bookingData?.data?.time_slot && (
              <p className="text-center text-primary-03 lg:text-2xl md:text-xl text-lg font-bold">{`${t('bookings.time_slot', { time_slot: bookingData?.data?.time_slot })}`}</p>
            )}
          </div>

          <QueryProvider>
            <Bookings bookingData={bookingData?.data} initialAvailableBookings={availableBookings?.data} />
          </QueryProvider>
        </section>
      )}
    </>
  )
}
