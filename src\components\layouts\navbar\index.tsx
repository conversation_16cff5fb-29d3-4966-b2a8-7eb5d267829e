"use client"

// components
import { TopNav } from '@/components/layouts/navbar/components/topNav'
import { MainNav } from '@/components/layouts/navbar/components/mainNav'
import { cn } from '@/utils/cn'
import { ComponentProps } from 'react'
import { IDoctorInfo } from '@/types/doctorInfo'

 function Navbar({ className , doctorInfo}: { className?: ComponentProps<'nav'>['className'] , doctorInfo: IDoctorInfo }) {


  return (
    <nav className={cn('w-full', className)}>
      <TopNav doctorInfo={doctorInfo} className="max-md:hidden" />
      <MainNav />
    </nav>
  )
}

export { Navbar }
