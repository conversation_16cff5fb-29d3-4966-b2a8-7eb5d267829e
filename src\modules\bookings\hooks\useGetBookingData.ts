'use client'

import { useEffect, useRef, useState } from 'react'
import { IAvailableBookings } from '@/types'
import useApi from '@/hooks/useApi'

interface UseGetBookingDataProps {
  date?: string
  clinic?: string
  type?: string
  enabled?: boolean
  initialData?: IAvailableBookings[] | null
}

interface UseGetBookingDataReturn {
  data: IAvailableBookings[] | null
  loading: boolean
  error: any
}

export function useGetBookingData({
  date = new Date().toISOString().split('T')[0],
  clinic,
  type = 'online',
  enabled = true,
  initialData = null,
}: UseGetBookingDataProps): UseGetBookingDataReturn {
  const { isPending, state, action } = useApi<IAvailableBookings[]>({
    path: '/get-available-bookings',
    searchParams: {
      date: date,
      type: type,
      ...(clinic && type === 'offline' && { clinic_id: clinic }),
    },
  })

  const [data, setData] = useState<IAvailableBookings[] | null>(initialData)
  const hasUsedInitialData = useRef(false)

  useEffect(() => {
    if (initialData && !hasUsedInitialData.current) {
      hasUsedInitialData.current = true
      setData(initialData)
      return
    }

    if (enabled) {
      action()
    }
  }, [date, clinic, type, enabled])

  useEffect(() => {
    if (state?.data?.data) {
      setData(state.data.data)
    }
  }, [state?.data?.data])

  return {
    data,
    loading: isPending,
    error: state.error,
  }
}
