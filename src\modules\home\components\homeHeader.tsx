// images
import BG from '/public/images/footer_nav_bg.png'

import { Navbar } from '@/components/layouts/navbar'
import HeroSection from '@/modules/home/<USER>/heroSection'
import { IDoctorInfo } from '@/types'

export default function HomeHeader({doctorInfo}: {doctorInfo: IDoctorInfo}) {
  return (
    <section className="relative w-full overflow-hidden">
      <div className="absolute inset-0 base_gradient py-10" />

      <div
        className="absolute inset-0 h-[75%] top-1/2 -translate-y-1/2 z-0"
        style={{
          backgroundImage: `url(${BG.src})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      <div className="relative z-10 ">
        <Navbar doctorInfo={doctorInfo} />
        <HeroSection />
      </div>
    </section>
  )
}
