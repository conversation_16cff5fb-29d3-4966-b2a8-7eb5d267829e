import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { apiService } from '@/services'

import SectionTitle from '@/components/shared/sectionTitle'
import { IQualification } from '@/types'
import QualificationItem from '@/modules/about/components/qualificationItem'

export const metadata: Metadata = {
  title: 'Qualifications',
}

export default async function QualificationsPage() {
  const t = await getTranslations()

  const data = await apiService({
    path: '/qualifications',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })
  return (
    <>
      {data?.data && data?.data?.items.length > 0 && (
        <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
          <SectionTitle title={t('about.qualifications')} className="text-center" />
          <div className="grid md:grid-cols-2 grid-cols-1 md:gap-8 gap-4 ">
            {data?.data?.items?.map((qualification: IQualification) => (
              <QualificationItem key={qualification.title} qualification={qualification} />
            ))}
          </div>
        </section>
      )}
    </>
  )
}
