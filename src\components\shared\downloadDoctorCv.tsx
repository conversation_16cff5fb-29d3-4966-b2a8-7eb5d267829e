'use client'

import { IDoctorInfo } from '@/types/doctorInfo'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Download } from 'lucide-react'

export default function DownloadDoctorCv({ doctorInfo }: { doctorInfo: IDoctorInfo['about'] }) {
  const t = useTranslations()

  const handleDownload = async () => {
    if (!doctorInfo?.profile_pdf) {
      toast.error(t('toast.no_profile_pdf'))
      return
    }

    try {
      const response = await fetch(doctorInfo?.profile_pdf)

      if (!response.ok) {
        console.error(`Failed to download file: ${response.statusText}`)
      }

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = 'doctor-profile.pdf'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      window.URL.revokeObjectURL(downloadUrl)
      toast.success(t('toast.download_profile_success'))
    } catch (error) {
      console.error('Download failed:', error)
      toast.error(t('toast.download_profile_error'))
    }
  }

  return (
    <Button
      variant="text"
      onClick={handleDownload}
      className="flex justify-start items-center gap-1 text-primary-02 font-bold lg:text-2xl sm:text-xl text-lg mt-4"
      disabled={!doctorInfo?.profile_pdf}
    >
      <Download className="size-4 text-primary-02" />
      {t('button.download_profile')}
    </Button>
  )
}
