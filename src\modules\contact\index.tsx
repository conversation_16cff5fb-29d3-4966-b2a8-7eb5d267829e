import SectionTitle from '@/components/shared/sectionTitle'
import { useTranslations } from 'next-intl'
import ContactForm from './components/contactForm/ContactForm'
import { IDoctorInfo } from '@/types'
import ContactInfo from './components/ContactInfo'

const Contact = (doctorInfo: IDoctorInfo) => {
  const t = useTranslations()
  return (
    <div className="container">
      <SectionTitle className="mt-12 mb-8 text-center" title={t('contact.contact_us')} />
      <ContactForm />
      <ContactInfo {...doctorInfo} />
    </div>
  )
}

export default Contact
