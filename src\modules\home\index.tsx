import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

// types
import { IActivity, IArticle, IFaqs, ITestimonial, IHome, IDoctorInfo } from '@/types'

// routes
import { Routes } from '@/routes/routes'

// shared
import { DoctorIn<PERSON> } from '@/components/shared/doctorInfo'
import SectionTitle from '@/components/shared/sectionTitle'
import Article from '@/components/shared/Article'
import { ActivityItem } from '@/components/shared/activitiyItem'
import MainLinkBtn from '@/components/shared/mainLinkBtn'
import { FaqsItem } from '@/components/shared/faqsItem'
import Testimonial from '@/components/shared/Testimonial'
import Statistics from '@/components/shared/Statistics'

export default async function Home({ doctorInfo, home }: { doctorInfo: IDoctorInfo; home: IHome }) {
  const t = await getTranslations()

  return (
    <>
      <Statistics className="lg:py-11 md:py-8 py-6" />

      <DoctorInfo doctorInfo={doctorInfo?.about} className="lg:py-11 md:py-8 py-6" />

      {home?.articles && (
        <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
          <SectionTitle title={t('articles.medical_articles')} className="text-center" />

          <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-8 ">
            {home?.articles.map((article: IArticle) => <Article key={article.id} {...article} />)}
          </div>

          <MainLinkBtn link={Routes.ARTICLES} text={t('button.rea_more')} />
        </section>
      )}

      {home?.activities && (
        <section className="lg:py-11 md:py-8 py-6  bg-primary-bg w-full">
          <div className="flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
            <SectionTitle title={t('nav.activities')} className="text-center" />

            {home?.activities.map((activity: IActivity, index: number) => (
              <ActivityItem key={activity.title} index={index} {...activity} className="w-full" />
            ))}

            <MainLinkBtn link={Routes.ACTIVITIES} text={t('button.rea_more')} />
          </div>
        </section>
      )}

      {home?.faqs && (
        <section className="lg:py-11 md:py-8 py-6 w-full">
          <div className="flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
            <SectionTitle title={t('nav.activities')} className="text-center" />

            {home?.faqs.map((faq: IFaqs) => <FaqsItem key={faq.question} {...faq} />)}

            <MainLinkBtn link={Routes.FAQS} text={t('button.rea_more')} />
          </div>
        </section>
      )}

      {home?.testimonials && (
        <section className="lg:py-11 md:py-8 py-6  bg-primary-bg w-full">
          <div className="flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
            <SectionTitle title={t('nav.testimonials')} className="text-center" />
            <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-8 ">
              {home?.testimonials.map((testimonial: ITestimonial) => (
                <Testimonial key={testimonial.title} {...testimonial} />
              ))}
            </div>
          </div>
        </section>
      )}
    </>
  )
}
