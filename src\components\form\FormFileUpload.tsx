import { cn } from '@/lib/utils'
import { useState, useRef, type ChangeEvent } from 'react'
import { Button } from '@/components/ui/button'
import { useTranslations } from 'next-intl'
import { useFormContext } from 'react-hook-form'
import { useFormWrapperContext } from '../core/FormWrapper'
import { FormField, FormItem, FormLabel } from '../ui/form'
import { CloudUpload, File, Trash2 } from 'lucide-react'
import { toast } from 'sonner'

type fileTypes =
  | 'image/*'
  | 'video/*'
  | 'audio/*'
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.ms-excel'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

interface FilesPreviewProps {
  selectedFiles: (File | IFileResponse)[] | File | IFileResponse
  removeImage: (index: number, url?: string) => void
}

interface FormFileUploadProps {
  label?: string
  name: string
  maxSize?: number // in MB
  maxLength?: number
  accept?: fileTypes
  multiple?: boolean
}

export function FormFileUpload({
  label,
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
}: FormFileUploadProps) {
  const t = useTranslations()
  const { errors } = useFormWrapperContext()
  const { control, setValue, getValues, watch } = useFormContext()

  const [isDragging, setIsDragging] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const selectedFiles = watch(name) || []

  const handleFileSelect = (files: FileList) => {
    // Validate max file number
    if (multiple && !validateMaxLength(Array.from(files)))
      return toast.error(t('validations.too_many_files', { maxLength }))

    Array.from(files).forEach((file) => {
      if (!file) return

      // Validate file size
      if (!validateMaxSize(file)) return toast.error(t('validations.file_too_large', { maxSize }))

      setValue(name, multiple ? [...(getValues(name) || []), file] : file)
    })
  }

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files

    if (files) {
      handleFileSelect(files)
    }
  }

  const removeImage = (index: number, url?: string) => {
    if (url) URL.revokeObjectURL(url)

    if (fileInputRef.current) fileInputRef.current.value = ''

    // Incase input not accepting multiple files
    if (!multiple) return setValue(name, null)

    // Incase input accepting multiple files
    const filteredValues = Array.from(selectedFiles).filter((_, i) => i !== index)
    setValue(name, filteredValues)
  }

  const validateMaxLength = (files: File[]): boolean => {
    const currentFiles = getValues(name) || []
    const selectedFilesLength = Array.isArray(currentFiles) ? currentFiles.length : currentFiles ? 1 : 0
    if (maxLength && Array.from(files).length + selectedFilesLength > maxLength) return false

    return true
  }

  const validateMaxSize = (file: File) => {
    if (file.size > maxSize * 1024 * 1024) return false

    return true
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = e.dataTransfer.files
    if (files) {
      handleFileSelect(files)
    }
  }

  function getAllowedFileTypesDescription(): string {
    const mimeToExtensions: Record<fileTypes, string[]> = {
      'image/*': ['*.jpeg', '*.jpg', '*.png', '*.gif', '*.webp'],
      'video/*': ['*.mp4', '*.mov', '*.avi'],
      'audio/*': ['*.mp3', '*.wav', '*.ogg'],
      'application/pdf': ['*.pdf'],
      'application/msword': ['*.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['*.docx'],
      'application/vnd.ms-excel': ['*.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['*.xlsx'],
    }

    return mimeToExtensions[accept].join(', ')
  }

  // Cleanup on unmount
  // useEffect(() => {
  //   return () => {
  //     if (selectedImage?.length) {
  //       selectedImage.forEach((f) => f.url && URL.revokeObjectURL(f.url))
  //     }
  //   }
  // }, [])

  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <div className="w-full flex flex-col justify-between gap-2">
          <label htmlFor={`image-upload-${name}`} className="flex flex-col gap-2 w-full">
            <FormItem>
              {label && <FormLabel>{label}</FormLabel>}
              <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                className={cn(
                  'w-full flex flex-col items-center gap-2 border-2 border-dashed rounded-lg p-7 transition-colors shadow-xs bg-transparent dark:bg-input/30 border-primary/20 hover:border-primary',
                  isDragging && 'border-primary',
                  errors[name] && 'bg-red-50 border-red-300 hover:border-red-400'
                )}
              >
                <CloudUpload size={24} />
                <p className="font-medium text-sm">{t('label.file_upload_placeholder_title')}</p>
                <p className="text-sm">
                  {t('label.file_upload_file_types', { accept: getAllowedFileTypesDescription() })}
                </p>
                <p className="text-sm text-center">
                  {`${t('label.file_upload_max_file_size', { maxSize })} ${maxLength && maxSize && t('label.and')} ${maxLength && t('label.file_upload_max_file_number', { maxLength })}`}
                </p>
                <input
                  multiple={multiple}
                  ref={fileInputRef}
                  id={`image-upload-${name}`}
                  type="file"
                  accept={accept}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </FormItem>
          </label>
          <FilesPreview selectedFiles={selectedFiles} removeImage={removeImage} />
        </div>
      )}
    />
  )
}

const FilesPreview = ({ selectedFiles, removeImage }: FilesPreviewProps) => {
  if (!selectedFiles) return <></>

  const filesAsArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2">
      {filesAsArray.map((file, index) => {
        const isExistingFile = 'path' in file
        const fileSize = isExistingFile ? 'N/A' : file.size
        const fileUrl = isExistingFile ? file.path : URL.createObjectURL(file)

        return (
          <div
            key={isExistingFile ? `${file.id}-${file.name}` : file.name}
            className="flex gap-2 justify-between items-center p-2 transition-shadow shadow-xs hover:shadow-md bg-transparent dark:bg-input/30 rounded-md"
          >
            <div className="flex gap-2 items-center">
              <div className="w-[60px] h-[60px] grid place-items-center relative overflow-hidden rounded-md">
                {file.type.startsWith('image/') ? (
                  <img alt={file.name} src={fileUrl} className="object-cover object-center w-full h-full" />
                ) : (
                  <File size={24} />
                )}
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-sm font-medium truncate max-w-[150px]">{file.name}</p>
                <p className="text-xs font-medium truncate max-w-[150px]">{file.type.split('/')[1]}</p>
                <p className="text-sm text-muted-foreground">
                  {/* write the file size in MB and if existing file show existing file */}
                  {isExistingFile
                    ? 'Existing file'
                    : typeof fileSize === 'number' && fileSize < 1024 * 1024
                      ? `${(fileSize / 1024).toFixed(2)} KB`
                      : typeof fileSize === 'number'
                        ? `${(fileSize / 1024 / 1024).toFixed(2)} MB`
                        : 'N/A'}
                </p>
              </div>
            </div>
            <Button
              type="button"
              variant="destructive"
              onClick={() => removeImage(index, isExistingFile ? undefined : fileUrl)}
            >
              <Trash2 size={24} />
            </Button>
          </div>
        )
      })}
    </div>
  )
}
