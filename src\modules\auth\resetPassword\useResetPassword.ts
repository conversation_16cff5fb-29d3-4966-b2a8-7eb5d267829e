import { IResetPassword } from '@/types'
import { getCookie, deleteCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { toast } from 'sonner'
import { useEffect } from 'react'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'

const defaultValues: IResetPassword = {
  email: '',
  code: '',
  password: '',
  password_confirmation: '',
}

const useResetPassword = () => {
  const t = useTranslations()
  const code = getCookie('code')
  const email = getCookie('em')

  useEffect(() => {
    if (!email || !code) {
      toast.error(t('auth.session_expired'))
      redirect(Routes.AUTH.FORGOT_PASSWORD)
    }
  }, [email, code, t])

  const { action, isPending } = useApi({
    path: '/auth/reset-password',
    method: 'POST',
    handleSuccess: false,
    handleError: false,
    onSuccess: (state) => {
      deleteCookie('code')
      deleteCookie('em')
      redirect(Routes.AUTH.LOGIN)
    },
  })

  const onSubmit = async (payload: IResetPassword) => {
    if (!email || !code) {
      toast.error(t('auth.missing_required_data'))
      return
    }

    const submitData = {
      ...payload,
      email: email as string,
      code: code as string,
    }

    const formdata = new FormData()
    formdata.append('email', submitData.email)
    formdata.append('code', submitData.code)
    formdata.append('password', submitData.password)
    formdata.append('password_confirmation', submitData.password_confirmation)
    action(formdata)
  }

  return {
    t,
    isPending,
    onSubmit,
    defaultValues,
  }
}

export default useResetPassword
