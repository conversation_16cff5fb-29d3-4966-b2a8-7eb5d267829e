import { MoreHorizontal } from 'lucide-react'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { SidebarMenuAction, useSidebar } from '@/components/ui/sidebar'
import { IMenuItem } from './sidebarLinks'
import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { useTranslations } from 'next-intl'

export function SidebarMenuComponent({ menuItems }: { menuItems: IMenuItem[] }) {
  const t = useTranslations()
  const { isMobile } = useSidebar()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuAction showOnHover>
          <MoreHorizontal />
          <span className="sr-only">More</span>
        </SidebarMenuAction>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-48 rounded-lg"
        side={isMobile ? 'bottom' : 'right'}
        align={isMobile ? 'end' : 'start'}
      >
        {menuItems.map((item) => (
          <DropdownMenuItem>
            {item.icon && <item.icon />}
            {item.url && <Link to={item.url}>{item.title && t(`navbar.${item.title}`)}</Link>}
            {item.onClick && <Button onClick={item.onClick}>{item.title && item.title}</Button>}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
