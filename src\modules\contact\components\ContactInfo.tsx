import { Doctor<PERSON>ontactLinks } from '@/components/shared/doctorContactLinks'
import { IDoctorInfo } from '@/types'
import { useTranslations } from 'next-intl'
import GoogleMap from './GoogleMap'

const ContactInfo = (doctorInfo: IDoctorInfo) => {
  const t = useTranslations()
  const location = doctorInfo?.clinics?.[0]?.location
  return (
    <div className="grid md:grid-cols-2 grid-cols-1 gap-8 mt-14 mb-10">
      {location && <GoogleMap url={location} />}
      <DoctorContactLinks
        itemClassName="text-primary-02 text-[28px]"
        titleClassName="!text-[36px] mb-8 mt-2"
        iconClassName="w-12 h-12 rounded-full bg-primary-03-bg flex justify-center items-center"
        title={t('contact.find_us_here')}
        iconWidth="max-w-[40px]"
        {...doctorInfo}
      />
    </div>
  )
}

export default ContactInfo
