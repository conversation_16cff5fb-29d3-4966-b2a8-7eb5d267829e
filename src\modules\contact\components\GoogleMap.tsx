const GoogleMap = ({ url }: { url: string }) => {
  const parsedUrl = new URL(url)

  const query = parsedUrl.searchParams.get('query')

  const [lat, lng] = query?.split(',').map(Number) || [0, 0]

  return (
    <iframe
      src={`https://www.google.com/maps?q=${lat},${lng}&hl=en&z=14&output=embed`}
      height="600"
      style={{ border: 0 }}
      allowFullScreen
      loading="lazy"
      className="rounded-[24px] mx-auto w-full"
      referrerPolicy="no-referrer-when-downgrade"
    ></iframe>
  )
}

export default GoogleMap
