'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput } from '@/components/form'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { loginSchema } from '../schema'
import useLogin from './useLogin'
import HeaderPage from '../components/HeaderPage'
import { Routes } from '@/routes/routes'

const defaultValues = {
  email: '',
  password: '',
}

const Login = () => {
  const { t, handleSubmit, isPending } = useLogin()
  return (
    <>
      <HeaderPage title="login_to_account" description="welcome_back" />
      <FormWrapper schema={loginSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <FormInput
          name="email"
          className="min-w-[300px] sm:min-w-[380px]"
          type="email"
          label={t('label.email')}
          placeholder={t('label.email')}
        />
        <FormPasswordInput name="password" label={t('label.password')} placeholder={t('label.password')} />
        <Link className="text-primary-02 font-bold block w-full text-end" href={Routes.AUTH.FORGOT_PASSWORD}>
          {t('auth.forgot_password')}
        </Link>
        <div className="text-center">
          <Button className="mt-9 mb-3" type="submit" disabled={isPending}>
            {isPending ? (
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {t('auth.sending')}
              </div>
            ) : (
              t('auth.login')
            )}
          </Button>
          <p className="text-primary-02 font-bold">
            {t('auth.no_account_yet')}
            <Link className="text-primary-01" href={Routes.AUTH.REGISTER}>
              {t('auth.register_now')}
            </Link>
          </p>
        </div>
      </FormWrapper>
    </>
  )
}

export default Login
