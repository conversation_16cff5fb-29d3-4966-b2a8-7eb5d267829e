import useApi from '@/hooks/useApi'
import { IContact } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

const defaultValues: IContact = {
  email: '',
  name: '',
  message: '',
}

const useContactForm = () => {
  const t = useTranslations()

  const { submit, isPending } = useApi({
    path: '/contact-us',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      toast.success(t('contact.send_message_success'))
    },
  })

  const handleSubmit = async (payload: IContact) => {
    const formdata = new FormData()
    formdata.append('email', payload.email)
    formdata.append('code', payload.name)
    formdata.append('password', payload.message)
    submit(formdata)
  }

  return {
    t,
    isPending,
    handleSubmit,
    defaultValues,
  }
}

export default useContactForm
