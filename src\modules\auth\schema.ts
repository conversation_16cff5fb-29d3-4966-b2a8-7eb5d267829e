import { passwordSchema } from '@/lib/schema'
import { object, ref, string } from 'yup'

export const registerSchema = object({
  first_name: string().required(),
  last_name: string().required(),
  phone: string().required(),
  email: string().required(),
  password: passwordSchema(),
  password_confirmation: string()
    .oneOf([ref('password')], 'Passwords must match')
    .required(),
})
export const loginSchema = object({
  email: string().required(),
  password: passwordSchema(),
})

export const forgotPasswordSchema = object({
  email: string().required(),
})

export const verifyResetCodeSchema = object({
  code: string().required(),
})

export const resetPasswordSchema = object({
  password: passwordSchema(),
  password_confirmation: string()
    .oneOf([ref('password')], 'Passwords must match')
    .required(),
})
