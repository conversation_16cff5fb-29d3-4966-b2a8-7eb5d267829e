'use client'

import { toast } from 'sonner'
import { signOut } from 'next-auth/react'

// Types
import type { IError } from './types'

type TNotifyType = 'error' | 'success' | 'warning' | 'info'

const useError = () => {
  const handleErrors = (error: IError, type: TNotifyType = 'error', fallbackMessage = 'Something went wrong') => {
    // Handle 401 errors by signing out the user
    if (typeof error === 'object' && error !== null && (error as any).status === 401) {
      signOut({ callbackUrl: '/auth/login', redirect: true })
      return
    }

    if (typeof error === 'object' && error !== null) {
      Object.values(error).forEach((msg: any) => {
        if (Array.isArray(msg)) {
          msg.forEach((m) => {
            toast[type](String(m) || fallbackMessage)
          })
        } else {
          toast[type](String(msg) || fallbackMessage)
        }
      })
    } else if (typeof error === 'string') {
      toast[type](error)
    } else {
      toast[type](fallbackMessage)
    }
  }

  return {
    handleErrors,
  }
}

export default useError
