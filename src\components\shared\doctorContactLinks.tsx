import { ComponentProps, Fragment } from 'react'
import { IDoctorInfo } from '@/types/doctorInfo'

import { cn } from '@/lib/utils'

// icons
import { LocationIcon } from '@/components/icons/social/LocationIcon'
import { EmailIcon } from '@/components/icons/social/EmailIcon'
import { WhatsAppWithBg } from '@/components/icons/social/WhatsAppWithBg'
import { PhoneIcon } from 'lucide-react'

interface Props extends Pick<IDoctorInfo, 'email' | 'phone' | 'whatsapp' | 'clinics'> {
  title?: string
  className?: ComponentProps<'div'>['className']
  titleClassName?: ComponentProps<'h5'>['className']
  listClassName?: ComponentProps<'ul'>['className']
  itemClassName?: ComponentProps<'li'>['className']
  iconClassName?: ComponentProps<'span'>['className']
  iconWidth?: string
}

const DoctorContactLinks = ({
  email,
  phone,
  whatsapp,
  clinics,
  listClassName,
  itemClassName,
  titleClassName,
  iconClassName,
  title,
  className,
  iconWidth,
}: Props) => {
  const contactItems = [
    {
      link: `mailto:${email}`,
      icon: <EmailIcon className={cn('max-w-[16px]', iconWidth)} />,
      type: 'email',
      text: email,
    },
    {
      link: `tel:${phone}`,
      icon: <PhoneIcon className={cn('max-w-[16px] fill-gray-01 stroke-gray-01', iconWidth)} />,
      type: 'phone',
      text: phone,
    },
    {
      link: whatsapp ? `https://wa.me/${whatsapp.replace(/\D/g, '')}` : '#',
      icon: <WhatsAppWithBg className={cn('max-w-[16px] fill-gray-01', iconWidth)} />,
      type: 'whatsapp',
      text: whatsapp,
    },
  ]

  return (
    <div className={cn('flex flex-col lg:gap-6 md:gap-4 gap-3', className)}>
      {title && <h5 className={cn('text-primary-01 font-bold md:text-lg text-base', titleClassName)}>{title}</h5>}
      <ul className={cn('list-inside lg:space-y-6 md:space-y-4 space-y-3', listClassName)}>
        {contactItems.map((item) => (
          <Fragment key={item.link}>
            {item.text && (
              <li className={cn('text-primary-03 text-lg flex justify-start items-center gap-1', itemClassName)}>
                <span className={iconClassName}>{item.icon}</span>
                <a
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  dir={item.type === 'phone' || item.type === 'whatsapp' ? 'ltr' : 'rtl'}
                >
                  {item.text}
                </a>
              </li>
            )}
          </Fragment>
        ))}

        {/* list of clinics */}
        {clinics && clinics.length > 0 && (
          <li className={cn('text-primary-03 text-lg flex justify-start items-start gap-3', itemClassName)}>
            <span className={iconClassName}>
              <LocationIcon className={cn('max-w-[16px]', iconWidth)} />
            </span>

            <ul className="list-inside list-disc">
              {clinics.map((clinic) => (
                <li key={clinic.name}>
                  <a
                    href={clinic.location}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cn('text-primary-03 text-lg hover:underline', itemClassName)}
                  >
                    {clinic.name}
                  </a>
                </li>
              ))}
            </ul>
          </li>
        )}
      </ul>
    </div>
  )
}

export { DoctorContactLinks }
