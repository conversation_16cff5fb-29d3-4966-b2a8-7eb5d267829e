import { Routes } from '@/routes/routes'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

// types
import { IDoctorInfo } from '@/types/doctorInfo'
import { IQualification } from '@/types'

// shared components
import { DoctorInfo as DoctorInfoComponent } from '@/components/shared/doctorInfo'
import SectionTitle from '@/components/shared/sectionTitle'
import MainLinkBtn from '@/components/shared/mainLinkBtn'
import IconWithBg from '@/components/shared/iconWithBg'

// images
import AboutClinicOneImg from '/public/images/about_clinic_1.jpg'
import AboutClinic1Icon from '/public/icons/breakHeart.svg'
import Echocardiogram from '/public/icons/Echocardiogram.svg'
import Pacemaker from '/public/icons/Pacemaker.svg'
import ECG from '/public/icons/(ECG).svg'
import AboutClinicTwoImg from '/public/images/about_clinic_2.png'
import Holter from '/public/icons/holter.svg'
import bloodPressureMonitor from '/public/icons/blood_pressure_monitor.svg'

// about components
import QualificationItem from '@/modules/about/components/qualificationItem'
import HeaderSection from '@/modules/about/components/headerSection'
import AboutClinic from '@/modules/about/components/aboutClinic'
import OurValues from '@/modules/about/components/ourValues'
import Statistics from '@/components/shared/Statistics'

export default function About({ doctorInfo }: { doctorInfo: IDoctorInfo }) {
  const t = useTranslations()

  const aboutClinicOne = {
    title: t('about.about_clinic_1'),
    image: AboutClinicOneImg,
    items: [
      {
        title: t('about.about_clinic_1_1'),
        icon: (
          <IconWithBg
            className="bg-action-04-bg"
            icon={
              <Image
                src={AboutClinic1Icon}
                alt={t('about.about_clinic_1_1')}
                width={32}
                height={32}
                className="max-sm:w-[28px]"
              />
            }
          />
        ),
      },
      {
        title: t('about.about_clinic_1_2'),
        icon: (
          <IconWithBg
            className="bg-action-03-bg"
            icon={
              <Image
                src={Echocardiogram}
                alt={t('about.about_clinic_1_2')}
                width={32}
                height={32}
                className="max-sm:w-[28px]"
              />
            }
          />
        ),
      },
      {
        title: t('about.about_clinic_1_3'),
        icon: (
          <IconWithBg
            className="bg-action-06-bg"
            icon={
              <Image
                src={Pacemaker}
                alt={t('about.about_clinic_1_3')}
                width={32}
                height={32}
                className="max-sm:w-[28px]"
              />
            }
          />
        ),
      },
      {
        title: t('about.about_clinic_1_4'),
        icon: (
          <IconWithBg
            className="bg-action-02-bg"
            icon={
              <Image src={ECG} alt={t('about.about_clinic_1_4')} width={32} height={32} className="max-sm:w-[28px]" />
            }
          />
        ),
      },
    ],
  }

  const aboutClinicTwo = {
    title: t('about.about_clinic_2'),
    image: AboutClinicTwoImg,
    items: [
      {
        title: t('about.about_clinic_1_1'),
        icon: (
          <IconWithBg
            className="bg-action-04-bg"
            icon={
              <Image
                src={Holter}
                alt={t('about.about_clinic_1_1')}
                width={32}
                height={32}
                className="max-sm:w-[28px]"
              />
            }
          />
        ),
      },
      {
        title: t('about.about_clinic_1_2'),
        icon: (
          <IconWithBg
            className="bg-action-02-bg"
            icon={
              <Image
                src={Holter}
                alt={t('about.about_clinic_1_2')}
                width={32}
                height={32}
                className="max-sm:w-[28px]"
              />
            }
          />
        ),
      },
      {
        title: t('about.about_clinic_1_3'),
        icon: (
          <IconWithBg
            className="bg-action-03-bg"
            icon={
              <Image
                src={bloodPressureMonitor}
                alt={t('about.about_clinic_1_3')}
                width={32}
                height={32}
                className="max-sm:w-[28px]"
              />
            }
          />
        ),
      },
    ],
  }

  return (
    <>
      <HeaderSection doctorInfo={doctorInfo} className="lg:py-16 md:py-12 py-8" />
      {doctorInfo?.profile && doctorInfo?.qualifications.length > 0 && (
        <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
          <SectionTitle title={t('about.qualifications')} className="text-center" />
          <div className="grid md:grid-cols-2 grid-cols-1 md:gap-8 gap-4 ">
            {doctorInfo?.qualifications?.map((qualification: IQualification) => (
              <QualificationItem key={qualification.title} qualification={qualification} />
            ))}
          </div>

          <MainLinkBtn link={Routes.QUALIFICATIONS} text={t('button.rea_more')} />
        </section>
      )}
      <DoctorInfoComponent doctorInfo={doctorInfo?.about} className="lg:py-11 md:py-8 py-6 flex-row-reverse" />
      <AboutClinic {...aboutClinicOne} className="lg:py-11 md:py-8 py-6" />
      <AboutClinic {...aboutClinicTwo} className="lg:py-11 md:py-8 py-6 flex-row-reverse" />

      <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
        <SectionTitle title={t('about.our_values')} className="text-center" />
        <OurValues className="lg:py-11 md:py-8 py-6" />
      </section>

      <Statistics className="lg:py-11 md:py-8 py-6" />
    </>
  )
}
