'use client'

// Hooks
import { createContext, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import type { IQueryContextProps, IQueryProviderProps } from './types'

export const defaultValues: IQueryContextProps = {
  routeQuery: false,
  query: null,
  addQuery: () => {},
  removeQuery: () => {},
  addRouteQuery: () => {},
  removeRouteQuery: () => {},
  forwardAddQuery: () => {},
  forwardDeleteQuery: () => {},
  resetAllQueries: () => {},
  resetAllRouteQueries: () => {},
  forwardResetAllQueries: () => {},
  forwardQuery: {},
}

export const QueryContext = createContext<IQueryContextProps>(defaultValues)

const QueryProvider = ({ children, isRouteQuery = true }: IQueryProviderProps) => {
  const router = useRouter()

  const [query, setQuery] = useState<Record<string, string> | null>(null)

  const searchParams = useSearchParams()
  const addQuery = (newQuery: Record<string, string>) => {
    const previousQueries = query
    previousQueries?.page && delete previousQueries.page
    setQuery({ ...previousQueries, ...newQuery })
  }

  const removeQuery = (queryName: string | string[]) => {
    const queryObject = { ...query }
    if (Array.isArray(queryName)) {
      queryName.forEach((q) => delete queryObject[q])
    } else {
      delete queryObject[queryName]
    }
    queryObject?.page && delete queryObject.page
    setQuery(!!queryObject && !!Object.entries(queryObject).length ? queryObject : null)
  }

  const addRouteQuery = (queryName: string, queryValue: string) => {
    const params = new URLSearchParams(searchParams.toString())

    if (params.get('page') && queryName !== 'page') params.delete('page')
    params.set(queryName, queryValue)

    router.push(`${window.location.pathname}?${params.toString()}`)
  }

  const addRouteQueries = (query: Record<string, string>) => {
    const currentParams = new URLSearchParams(searchParams.toString())

    // Merge existing params with the new query
    Object.entries(query).forEach(([key, value]) => {
      currentParams.set(key, value)
    })

    // if (Object.keys(query).some((key: string) => key !== 'page')) {
    //   currentParams.set('page', '1')
    // }

    // Update the search params
    router.push(`${window.location.pathname}?${currentParams.toString()}`)
  }

  const removeRouteQuery = (queryName: string | string[]) => {
    const params = new URLSearchParams(searchParams.toString())

    if (params.get('page') && queryName !== 'page') params.delete('page')
    if (typeof queryName === 'object') {
      queryName.forEach((query) => {
        params.delete(query)
      })
    } else {
      params.delete(queryName)
    }

    router.push(`${window.location.pathname}?${params.toString()}`)
  }

  const resetAllQueries = () => {
    setQuery(null)
  }

  const resetAllRouteQueries = () => {
    router.push(window.location.pathname)
  }

  const forwardAddQuery = isRouteQuery ? addRouteQueries : addQuery
  const forwardDeleteQuery = isRouteQuery ? removeRouteQuery : removeQuery
  const forwardResetAllQueries = isRouteQuery ? resetAllRouteQueries : resetAllQueries

  const objOfSearchParams = Object.fromEntries(Array.from(searchParams.entries()))

  const forwardQuery = isRouteQuery ? objOfSearchParams : query

  return (
    <QueryContext.Provider
      value={{
        routeQuery: isRouteQuery,
        query,
        addQuery,
        removeQuery,
        addRouteQuery,
        removeRouteQuery,
        forwardAddQuery,
        forwardDeleteQuery,
        forwardResetAllQueries,
        resetAllRouteQueries,
        resetAllQueries,
        forwardQuery,
      }}
    >
      {children}
    </QueryContext.Provider>
  )
}

export default QueryProvider
