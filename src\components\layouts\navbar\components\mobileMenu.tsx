'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useLocale } from 'next-intl'

// Routes
import { Routes } from '@/routes/routes'

// types
import { IMainNavItem } from '@/components/layouts/navbar/components/mainNav'

// Components
import { LocaleToggle } from '@/components/core/LocaleToggle'

import { Button } from '@/components/ui/button'
import { Drawer, DrawerClose, DrawerContent, DrawerHeader, DrawerTrigger } from '@/components/ui/drawer'

// Images
import Logo from '/public/logo.webp'
import { cn } from '@/lib/utils'

// Icons
import { Menu, X } from 'lucide-react'

function MobileMenu({ mobileNavItems }: { mobileNavItems: IMainNavItem[] }) {
  const locale = useLocale()

  // Determine drawer direction based on locale
  const drawerDirection = locale === 'ar' ? 'left' : 'right'

  return (
    <Drawer>
      <DrawerTrigger asChild className="md:hidden">
        <button aria-label="Toggle Menu" type="button" className="h-10 !w-fit">
          <Menu className="h-5 w-5" />
        </button>
      </DrawerTrigger>
      <DrawerContent className="md:hidden" data-vaul-drawer-direction={drawerDirection}>
        <DrawerHeader className="flex !flex-row items-start justify-between border-b pb-4">
          <Link href={Routes.HOME} className="flex items-center">
            <Image src={Logo} alt="logo" width={50} height={45} />
          </Link>
          <DrawerClose asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 justify-end">
              <X className="h-4 w-4" />
            </Button>
          </DrawerClose>
        </DrawerHeader>

        <div className="flex flex-col flex-1 px-4 py-6">
          {/* Navigation Items */}
          <nav className="flex flex-col space-y-4 mb-8">
            {mobileNavItems.map((item) => (
              <DrawerClose asChild key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center gap-3 px-3 py-2 rounded-lg text-lg font-medium transition-colors',
                    item.isActive ? 'text-primary-01' : 'text-primary-02 hover:bg-gray-100 hover:text-primary-01'
                  )}
                >
                  {item.image && <Image src={item.image} alt="home icon logo" width={20} height={20} />}
                  {item.label}
                </Link>
              </DrawerClose>
            ))}
            {/* Locale Toggle */}
            <LocaleToggle />
          </nav>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export { MobileMenu }
