'use server'

import { apiService, ApiServiceProps } from './apiService'

export type ActionServiceProps = ApiServiceProps

export async function actionService<T>(props: ActionServiceProps, _: any, body: any): Promise<ActionServiceReturn<T>> {
  const res: any = await apiService({ body, ...props })

  return {
    status: res.status,
    ...(res && { data: res }),
    message: res?.message || 'Success',
    error: res.status === 422 ? res.errors : res.message,
  }
}
