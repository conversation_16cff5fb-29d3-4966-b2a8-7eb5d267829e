'use client'

import { useTranslations } from 'next-intl'
import { IForgetPassword } from '@/types'
import { toast } from 'sonner'
import { setCookie } from 'cookies-next'
import { redirect } from 'next/navigation'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'
import useError from '@/hooks/useError'

const defaultValues: IForgetPassword = {
  email: '',
}

const useForgetPassword = () => {
  const t = useTranslations()
  const { handleErrors } = useError()
  const { action, isPending } = useApi({
    path: '/auth/forgot-password',
    method: 'POST',
    handleSuccess: false,
    handleError: false,
    onSuccess: (state) => {
      toast.success(t('auth.reset_code_sent'))
      redirect(Routes.AUTH.VERIFY_RESET_CODE)
    },
    onError: (error) => {
      handleErrors(error.error)
      if (error.status === 400) {
        redirect(Routes.AUTH.VERIFY_RESET_CODE)
      }
    },
  })

  const handleSubmit = (payload: IForgetPassword) => {
    setCookie('em', payload.email)
    const formdata = new FormData()
    formdata.append('email', payload.email)

    action(formdata)
  }

  return {
    t,
    isPending,
    handleSubmit,
    defaultValues,
  }
}

export default useForgetPassword
