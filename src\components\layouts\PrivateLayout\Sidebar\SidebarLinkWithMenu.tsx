import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'
import { ISidebarLink } from './sidebarLinks'
import { Link, matchPath, useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { SidebarMenuComponent } from './SidebarMenuComponent'
import { useTranslations } from 'next-intl'
import { cn } from '@/lib/utils'

export const SidebarLinkWithMenu = ({ item }: { item: ISidebarLink }) => {
  const t = useTranslations()
  const location = useLocation()
  const isActive = item.groupName && !!matchPath({ path: item.groupName || '', end: false }, location.pathname)

  return (
    <SidebarMenuItem key={item.title}>
      <SidebarMenuButton asChild className={cn('cursor-pointer', isActive && 'bg-primary/20')}>
        {item.url ? (
          <Link to={item.url}>
            {item.icon && <item.icon />}
            {item.title && <span>{t(`navbar.${item.title}`)}</span>}
          </Link>
        ) : (
          <Button>
            {item.icon && <item.icon />}
            {item.title && <span>{t(`navbar.${item.title}`)}</span>}
          </Button>
        )}
      </SidebarMenuButton>
      {item.menu && <SidebarMenuComponent menuItems={item.menu} />}
    </SidebarMenuItem>
  )
}
