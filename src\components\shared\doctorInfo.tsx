'use client'

import { cn } from '@/lib/utils'
import Image from 'next/image'
import { ComponentProps } from 'react'
import { IDoctorInfo } from '@/types/doctorInfo'
import { useTranslations } from 'next-intl'
import SectionTitle from './sectionTitle'
import DownloadDoctorCv from './downloadDoctorCv'

const DoctorInfo = ({
  className,
  doctorInfo,
}: {
  className?: ComponentProps<'div'>['className']
  doctorInfo: IDoctorInfo['about']
}) => {
  const t = useTranslations()

  return (
    <section className={cn('container flex justify-between gap-4 lg:gap-8 items-center max-md:flex-wrap', className)}>
      {doctorInfo?.image ? (
        <Image
          src={doctorInfo?.image}
          alt={t('name')}
          className="rounded-3xl w-full  h-auto max-h-[479px] object-cover object-top lg:!basis-1/2 !basis-full"
          width={548}
          height={479}
          priority
        />
      ) : (
        <div className="w-full h-[479px]   bg-gray-01 rounded-3xl lg:!basis-1/2 !basis-full" />
      )}

      <article className="flex justify-start items-start flex-col !basis-full lg:!basis-1/2 gap-4">
        <SectionTitle title={t('about.about_dr_ahmed')} />
        {doctorInfo?.content && (
          <p className="text-primary-03 lg:text-2xl sm:xl text-lg font-normal">{doctorInfo?.content}</p>
        )}

        <DownloadDoctorCv doctorInfo={doctorInfo} />
      </article>
    </section>
  )
}

export { DoctorInfo }
