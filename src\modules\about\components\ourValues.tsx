import { ComponentProps } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import { cn } from '@/utils/cn'

// shared components
import IconWithBg from '@/components/shared/iconWithBg'

// images
import Sincerity from '/public/icons/Sincerity.svg'
import Respect from '/public/icons/Respect.svg'
import Excellence from '/public/icons/Excellence.svg'
import Integrity from '/public/icons/Integrity.svg'
import Compassion from '/public/icons/Compassion.svg'

export default function OurValues({ className }: { className?: ComponentProps<'section'>['className'] }) {
  const t = useTranslations()
  const items = [
    {
      image: (
        <IconWithBg
          className="bg-action-01-bg"
          icon={
            <Image
              src={Sincerity}
              alt={t('about.about_clinic_1_1')}
              width={32}
              height={32}
              className="max-sm:w-[28px]"
            />
          }
        />
      ),
      title: t('about.value_1_title'),
      description: t('about.value_1_description'),
    },
    {
      image: (
        <IconWithBg
          className="bg-action-03-bg"
          icon={
            <Image src={Respect} alt={t('about.about_clinic_1_1')} width={32} height={32} className="max-sm:w-[28px]" />
          }
        />
      ),
      title: t('about.value_2_title'),
      description: t('about.value_2_description'),
    },
    {
      image: (
        <IconWithBg
          className="bg-action-04-bg"
          icon={
            <Image
              src={Excellence}
              alt={t('about.about_clinic_1_1')}
              width={32}
              height={32}
              className="max-sm:w-[28px]"
            />
          }
        />
      ),
      title: t('about.value_3_title'),
      description: t('about.value_3_description'),
    },
    {
      image: (
        <IconWithBg
          className="bg-action-02-bg"
          icon={
            <Image
              src={Integrity}
              alt={t('about.about_clinic_1_1')}
              width={32}
              height={32}
              className="max-sm:w-[28px]"
            />
          }
        />
      ),
      title: t('about.value_4_title'),
      description: t('about.value_4_description'),
    },
    {
      image: (
        <IconWithBg
          className="bg-action-05-bg"
          icon={
            <Image
              src={Compassion}
              alt={t('about.about_clinic_1_1')}
              width={32}
              height={32}
              className="max-sm:w-[28px]"
            />
          }
        />
      ),
      title: t('about.value_5_title'),
      description: t('about.value_5_description'),
    },
  ]
  return (
    <section className={cn('w-full', className)}>
      {/* Large screens: 3+2 layout as shown in image */}
      <div className="max-lg:hidden flex flex-col gap-6">
        {/* Top row: 3 cards with center elevated using flexbox */}
        <div className="flex justify-center items-end gap-6 pb-8">
          {/* Left card - التميز */}
          <ValuesItem {...items[0]} className="translate-y-8" />

          {/* Center card - الاحترام (elevated) */}
          <ValuesItem {...items[1]} className="-translate-y-8" />

          {/* Right card - الإخلاص */}
          <ValuesItem {...items[2]} className="translate-y-8" />
        </div>

        {/* Bottom row: 2 cards centered */}
        <div className="flex justify-center gap-6">
          {/* النزاهة */}
          <ValuesItem {...items[3]} />

          {/* العطف */}
          <ValuesItem {...items[4]} />
        </div>
      </div>

      {/* Small/Medium screens: Basic 2-column grid */}
      <div className="lg:hidden grid sm:grid-cols-2 grid-cols-1 gap-5">
        {items.map((item) => (
          <ValuesItem key={item.title} {...item} />
        ))}
      </div>
    </section>
  )
}

const ValuesItem = ({
  image,
  title,
  description,
  className,
}: {
  image: React.ReactNode
  title: string
  description: string
  className?: ComponentProps<'article'>['className']
}) => {
  return (
    <article
      key={title}
      className={cn(
        'flex flex-col justify-center items-center lg:gap-8 sm:gap-6 p-4 sm:p-6 lg:p-8 rounded-[24px] basic_card_shadow bg-white lg:w-[366px] lg:h-[366px]',
        className
      )}
    >
      <div className="flex justify-center items-center gap-2">
        {image}
        <h3 className="text-primary-03 lg:text-3xl md:text-2xl text-xl font-semibold text-center">{title}</h3>
      </div>
      <p className="text-gray-01 lg:text-2xl sm:text-xl  text-lg font-normal text-center leading-relaxed flex-1">
        {description}
      </p>
    </article>
  )
}
