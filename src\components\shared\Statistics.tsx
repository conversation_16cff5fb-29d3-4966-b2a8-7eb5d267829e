import { cn } from '@/utils/cn'
import { ComponentProps, ReactNode } from 'react'

// components
import IconWithBg from '@/components/shared/iconWithBg'
import { Award, Bed, Star, Users } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

interface IProps {
  text: string
  rate: string
  iconContainerClassName?: ComponentProps<'div'>['className']
  icon: ReactNode
}

interface IStatistics extends IProps {
  id: string
}

export default async function Statistics({ className }: { className?: ComponentProps<'section'>['className'] }) {
  const t = await getTranslations()

  const staticts: IStatistics[] = [
    {
      id: 'staticts_1_rate',
      text: t('staticts.patients_rating'),
      rate: '4.5/5',
      iconContainerClassName: 'bg-action-01-bg',
      icon: <Star className="fill-action-01 stroke-action-01" size={32} />,
    },
    {
      id: 'staticts_2_experience',
      text: t('staticts.years_of_experience'),
      rate: '+10',
      iconContainerClassName: 'bg-action-02-bg',
      icon: <Award className="fill-action-02 stroke-action-02" size={32} />,
    },
    {
      id: 'staticts_3_treated',
      text: t('staticts.treated_patients'),
      rate: '+5000',
      iconContainerClassName: 'bg-action-03-bg',
      icon: <Users className="fill-action-03 stroke-action-03" size={32} />,
    },
    {
      id: 'staticts_4_surgeries',
      text: t('staticts.surgeries_performed'),
      rate: '+2000',
      iconContainerClassName: 'bg-action-04-bg',
      icon: <Bed className="fill-action-04 stroke-action-04" size={32} />,
    },
  ]

  return (
    <section className={cn('grid lg:grid-cols-4 sm:grid-cols-2 grid-cols-2 gap-4 container', className)}>
      {staticts.map((statict) => (
        <StatictsItem key={statict.id} {...statict} />
      ))}
    </section>
  )
}

function StatictsItem({ text, rate, iconContainerClassName, icon }: IProps) {
  return (
    <div className="flex flex-col items-center gap-1 text-center">
      <IconWithBg className={iconContainerClassName} icon={icon} />
      <p className="text-primary-03 lg:text-[28px] md:text-[24px] sm:text-[20px] text-[18px] font-[900]">{rate}</p>
      <p className="text-gray-01 lg:text-[22px] md:text-[18px] sm:text-base text-sm font-normal">{text}</p>
    </div>
  )
}
