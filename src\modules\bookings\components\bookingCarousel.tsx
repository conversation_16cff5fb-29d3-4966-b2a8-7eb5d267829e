// ui components
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel'

// types
import AvailableBookItemCard from '@/modules/bookings/components/availableBookItemCard'
import { IAvailableBookings } from '@/types'
import { useLocale } from 'next-intl'

export interface IBookingCarouselProps {
  availableBookings: IAvailableBookings[]
  date: string
  day: string
  onBookingSelect: (from: string, to: string) => void
  isBookingSelected: (from: string, to: string) => boolean
}

export default function BookingCarousel({
  availableBookings = [],
  date,
  day,
  onBookingSelect,
  isBookingSelected,
}: IBookingCarouselProps) {
  const language = useLocale()
  const isRtl = language === 'ar'

  return (
    <article className="w-full min-h-48" dir={isRtl ? 'rtl' : 'ltr'}>
      <Carousel
        opts={{
          align: 'start',
          loop: true,
          slidesToScroll: 1,
          direction: isRtl ? 'rtl' : 'ltr',
        }}
        className="w-full min-h-fit"
      >
        <CarouselContent className="py-5 -mx-4">
          {availableBookings.map((booking, index) => (
            <CarouselItem key={`booking-${index}`} className="px-2 basis-full sm:basis-1/2 md:basis-1/3 lg:!basis-1/4">
              <AvailableBookItemCard
                onClick={() => onBookingSelect(booking.from, booking.to)}
                from={booking.from}
                to={booking.to}
                date={date}
                isSelected={isBookingSelected(booking.from, booking.to)}
                day={day}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        {availableBookings?.length > 0 && (
          <>
            <CarouselPrevious className=" !size-[48px] !min-w-fit z-50 border-none shadow-2xl shadow-primary-02  ms-4" />
            <CarouselNext className=" !size-[48px] !min-w-fit z-50 border-none shadow-2xl shadow-primary-02" />
          </>
        )}
      </Carousel>
    </article>
  )
}
