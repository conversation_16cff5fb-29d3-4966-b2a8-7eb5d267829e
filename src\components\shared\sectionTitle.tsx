import { cn } from '@/utils/cn'
import { ComponentProps } from 'react'

interface IProps {
  title: string
  className?: ComponentProps<'div'>['className']
}

export default function SectionTitle({ title, className }: IProps) {
  return (
    <h4
      className={cn(
        'text-primary-01 lg:text-[36px] md:text-[28px] sm:text-[24px] text-[20px] font-bold text-center',
        className
      )}
    >
      {title}
    </h4>
  )
}
