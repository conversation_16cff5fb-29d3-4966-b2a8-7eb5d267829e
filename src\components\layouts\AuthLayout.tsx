import Image from 'next/image'
import { PropsWithChildren } from 'react'
import { useLocale } from 'next-intl'
import Logo from '/public/icons/logo.svg'
import RegisterBg from '/public/images/auth-bg.png'

const AuthLayoutComponent: React.FC<PropsWithChildren> = ({ children }) => {
  const locale = useLocale()
  const isRtl = locale === 'ar'

  return (
    <div className={`relative min-h-screen ${isRtl ? 'auth-layout-rtl' : 'auth-layout-ltr'}`}>
      <Image src={RegisterBg} alt="Auth background" fill className="object-cover -z-10" priority />

      <main className="relative z-10 grid grid-cols-1 lg:grid-cols-2 min-h-screen">
        <div className={`hidden lg:block ${isRtl ? 'lg:order-2' : 'lg:order-1'}`}></div>
        <div
          className={`flex flex-col gap-4 justify-center items-center px-14 py-12 bg-white ${isRtl ? 'lg:order-1' : 'lg:order-2'}`}
        >
          <Image alt="logo" height={120} width={130} src={Logo} />
          {children}
        </div>
      </main>
    </div>
  )
}

export default AuthLayoutComponent
