import { IClinic } from "./doctorInfo"


export interface IBookingData {
  clinics: IClinic[]
  availability_days: string
  time_slot: string
}

export interface IAvailableBookings {

  from: string
  to: string
}

export interface IBooking { 
  id: number
  type: string
  date: string
  from: string
  to: string
  rating: number | null
  status: string
  client_attended: boolean
}

export interface IBookingDataResponse {
    items: IBooking[],
    pagination: IPagination
}

export interface ICreatBooking {
    type: 'online' | 'offline'
    clinic_id?: string 
    date: string
    from: string
    to: string
}