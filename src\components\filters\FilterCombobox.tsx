import { useQuery } from '@/store/queryContext/useQueryContext'
import { Combobox, ComboboxProps } from '../ui/combobox'
import { Label } from '../ui/label'
import { useTranslations } from 'next-intl'

export interface FilterComboboxProps<T extends { label: string; value: string }> extends ComboboxProps<T> {
  name: string
  label?: string
  placeholder?: string
}

const FilterCombobox = ({
  name,
  label,
  placeholder,
  ...props
}: FilterComboboxProps<{ label: string; value: string }>) => {
  const { forwardAddQuery, forwardDeleteQuery, forwardQuery } = useQuery()
  const t = useTranslations()

  const selectedValue = forwardQuery?.[name]

  const handleSelectFilter = (value: { label: string; value: string } | null) => {
    if (value) forwardAddQuery({ [name]: value.value })
  }

  const handleClearFilter = () => {
    forwardDeleteQuery(name)
  }

  return (
    <>
      {label && <Label htmlFor={name}>{label}</Label>}
      <Combobox
        className="w-full"
        name={name}
        {...props}
        value={selectedValue ? { label: selectedValue, value: selectedValue } : null}
        onClear={handleClearFilter}
        onChange={handleSelectFilter}
        placeholder={placeholder || t('label.select_option')}
      />
    </>
  )
}

export default FilterCombobox
