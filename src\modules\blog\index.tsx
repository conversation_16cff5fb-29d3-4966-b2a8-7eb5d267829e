import SectionTitle from '@/components/shared/sectionTitle'
import BlogFilters from './components/BlogFilters'
import ArticlesList from './components/ArticlesList'
import { useTranslations } from 'next-intl'
import { IArticle } from '@/types'

const BlogList = ({ data }: { data: TData<IArticle> }) => {
  const t = useTranslations()
  return (
    <div className="container">
      <SectionTitle title={t('articles.medical_articles')} className="mt-16" />
      <BlogFilters />
      <ArticlesList articles={data.items} />
    </div>
  )
}

export default BlogList
