import { ComponentProps } from 'react'
import { getTranslations } from 'next-intl/server'
import { cn } from '@/utils/cn'

// components
import { SocialLinks } from '@/components/shared/socialLinks'

// types
import { IDoctorInfo } from '@/types/doctorInfo'

async function BottomFooter({
  doctorInfo,
  className,
}: {
  doctorInfo: IDoctorInfo
  className?: ComponentProps<'div'>['className']
}) {
  const t = await getTranslations()

  const currentYear = new Date().getFullYear()

  return (
    <div
      className={cn(
        'container flex items-center md:justify-between justify-center gap-2 max-md:flex-col lg:py-6 py-4',
        className
      )}
    >
      <SocialLinks {...doctorInfo?.social} whatsapp={doctorInfo?.whatsapp} />

      <p className="text-primary-03 font-normal text-base">
        {`${t('footer.copyright')} ${currentYear} – ${t('name')}`}
      </p>
    </div>
  )
}

export { BottomFooter }
