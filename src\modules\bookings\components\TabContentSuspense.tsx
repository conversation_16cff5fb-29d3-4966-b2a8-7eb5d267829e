import { Suspense } from 'react'
import Loading from '@/components/core/Loading'
import { useTranslations } from 'next-intl'
import { IAvailableBookings } from '@/types'
import { Info } from 'lucide-react'

interface ITabContentSuspenseProps {
  children: React.ReactNode
  availableBookings: IAvailableBookings[] | null | undefined
  isLoading: boolean
}

function TabContentLoading() {
  return (
    <div className="w-full h-48 flex justify-center items-center">
      <Loading size={32} />
    </div>
  )
}

function EmptyStateMessage() {
  const t = useTranslations()

  return (
    <div className="w-full h-48 flex flex-col justify-center items-center gap-0.5">
      <Info className="text-gray-02 size-16 mb-3" />
      <p className="text-primary-03 text-center text-xl font-medium">{t('bookings.no_bookings_available')}</p>
      <p className="text-gray-500 text-center text-lg">{t('bookings.try_different_date')}</p>
    </div>
  )
}

export default function TabContentSuspense({ children, availableBookings, isLoading }: ITabContentSuspenseProps) {
  if (isLoading) {
    return <TabContentLoading />
  }

  if (availableBookings && availableBookings.length === 0) {
    return <EmptyStateMessage />
  }

  return <Suspense fallback={<TabContentLoading />}>{children}</Suspense>
}
