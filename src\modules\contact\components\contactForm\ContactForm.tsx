'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { Button } from '@/components/ui/button'

import { contactSchema } from './schema'
import useContactForm from './useContactForm'
import { FormInput, FormTextArea } from '@/components/form'

const ContactForm = () => {
  const { t, isPending, handleSubmit, defaultValues } = useContactForm()
  return (
    <FormWrapper schema={contactSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          name="name"
          className="min-w-[300px] sm:min-w-[380px]"
          label={t('label.full_name')}
          placeholder={t('label.full_name')}
        />
        <FormInput
          name="email"
          className="min-w-[300px] sm:min-w-[380px]"
          type="email"
          label={t('label.email')}
          placeholder={t('label.email')}
        />
      </div>
      <FormTextArea name="message" label={t('label.message')} placeholder={t('label.message')} />

      <div className="text-center">
        <Button className="mt-9 mb-3" type="submit" loading={isPending}>
          {t('contact.send')}
        </Button>
      </div>
    </FormWrapper>
  )
}

export default ContactForm
