import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { apiService } from '@/services'

import SectionTitle from '@/components/shared/sectionTitle'
import { FaqsItem } from '@/components/shared/faqsItem'
import { IFaqs } from '@/types/faqs'

export const metadata: Metadata = {
  title: 'Faqs',
}

export default async function FaqsPage() {
  const t = await getTranslations()

  const data = await apiService({
    path: '/faqs',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })
  return (
    <>
      {data?.data && data?.data?.items.length > 0 && (
        <section className="lg:py-11 md:py-8 py-6 flex flex-col lg:gap-7 md:gap-5 gap-3 items-center container">
          <SectionTitle title={t('nav.faqs')} className="text-center" />
          {data?.data?.items?.map((faqs: IFaqs) => <FaqsItem key={faqs.question} {...faqs} />)}
        </section>
      )}
    </>
  )
}
