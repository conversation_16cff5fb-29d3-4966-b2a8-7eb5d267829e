import { Metadata } from 'next'

import { apiService } from '@/services'

import About from '@/modules/about'

export const metadata: Metadata = {
  title: 'About',
}

export default async function AboutPage() {
  const doctorInfo = await apiService({
    path: '/doctor-info',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })

  return <About doctorInfo={doctorInfo?.data} />
}
